#!/usr/bin/env python3
"""
验证 Qwen2MoE 实现与 PR #18343 DeepSeek-V2 修改的对齐性。

这个脚本检查关键的实现模式是否与 PR #18343 中的修改保持一致。
"""

import ast
import sys

def check_enable_eplb_parameter():
    """检查 enable_eplb 参数是否正确添加到所有相关类中。"""
    print("检查 enable_eplb 参数...")
    
    file_path = 'vllm/model_executor/models/qwen2_moe.py'
    with open(file_path, 'r') as f:
        content = f.read()
    
    # 检查关键类是否有 enable_eplb 参数
    required_classes = [
        'Qwen2MoeSparseMoeBlock',
        'Qwen2MoeDecoderLayer'
    ]
    
    for class_name in required_classes:
        if f"class {class_name}" in content and "enable_eplb: bool = False" in content:
            print(f"  ✓ {class_name} 有 enable_eplb 参数")
        else:
            print(f"  ✗ {class_name} 缺少 enable_eplb 参数")
            return False
    
    return True

def check_config_acquisition():
    """检查配置获取方式是否与 PR #18343 一致。"""
    print("检查配置获取方式...")
    
    file_path = 'vllm/model_executor/models/qwen2_moe.py'
    with open(file_path, 'r') as f:
        content = f.read()
    
    # 检查关键配置获取模式
    required_patterns = [
        "from vllm.config import get_current_vllm_config",
        "vllm_config = get_current_vllm_config()",
        "parallel_config = vllm_config.parallel_config",
        "self.enable_eplb = enable_eplb",
        "self.n_redundant_experts = parallel_config.num_redundant_experts",
        "self.n_logical_experts = config.num_experts",
        "self.n_physical_experts = (self.n_logical_experts +",
        "enable_eplb = vllm_config.parallel_config.enable_eplb"
    ]
    
    for pattern in required_patterns:
        if pattern in content:
            print(f"  ✓ 找到模式: {pattern}")
        else:
            print(f"  ✗ 缺少模式: {pattern}")
            return False
    
    return True

def check_fusedmoe_parameters():
    """检查 FusedMoE 参数传递是否正确。"""
    print("检查 FusedMoE 参数传递...")
    
    file_path = 'vllm/model_executor/models/qwen2_moe.py'
    with open(file_path, 'r') as f:
        content = f.read()
    
    # 检查 FusedMoE 调用中的关键参数
    required_fusedmoe_params = [
        "enable_eplb=self.enable_eplb",
        "num_redundant_experts=self.n_redundant_experts"
    ]
    
    for param in required_fusedmoe_params:
        if param in content:
            print(f"  ✓ FusedMoE 参数: {param}")
        else:
            print(f"  ✗ 缺少 FusedMoE 参数: {param}")
            return False
    
    return True

def check_parameter_propagation():
    """检查参数传播链是否完整。"""
    print("检查参数传播链...")
    
    file_path = 'vllm/model_executor/models/qwen2_moe.py'
    with open(file_path, 'r') as f:
        content = f.read()
    
    # 检查参数传播模式
    propagation_patterns = [
        # Model -> DecoderLayer
        "enable_eplb=enable_eplb",
        # DecoderLayer -> MoE Block  
        "enable_eplb=enable_eplb"
    ]
    
    propagation_count = 0
    for pattern in propagation_patterns:
        if pattern in content:
            propagation_count += 1
    
    if propagation_count >= 2:  # 至少应该有两处参数传播
        print(f"  ✓ 参数传播链完整 (找到 {propagation_count} 处)")
        return True
    else:
        print(f"  ✗ 参数传播链不完整 (只找到 {propagation_count} 处)")
        return False

def check_moe_attributes():
    """检查 MoE 属性设置是否正确。"""
    print("检查 MoE 属性设置...")
    
    file_path = 'vllm/model_executor/models/qwen2_moe.py'
    with open(file_path, 'r') as f:
        content = f.read()
    
    # 检查关键属性设置
    required_attributes = [
        "self.num_moe_layers",
        "self.num_expert_groups", 
        "self.num_logical_experts",
        "self.num_physical_experts",
        "self.num_local_physical_experts",
        "self.num_routed_experts",
        "self.num_redundant_experts",
        "self.moe_layers: list[FusedMoE]",
        "self.expert_weights"
    ]
    
    for attr in required_attributes:
        if attr in content:
            print(f"  ✓ 属性: {attr}")
        else:
            print(f"  ✗ 缺少属性: {attr}")
            return False
    
    return True

def check_interface_implementation():
    """检查 MixtureOfExperts 接口实现。"""
    print("检查 MixtureOfExperts 接口实现...")
    
    file_path = 'vllm/model_executor/models/qwen2_moe.py'
    with open(file_path, 'r') as f:
        content = f.read()
    
    # 检查接口实现
    interface_patterns = [
        "class Qwen2MoeForCausalLM(nn.Module, SupportsPP, MixtureOfExperts)",
        "from .interfaces import MixtureOfExperts",
        "def set_eplb_state("
    ]
    
    for pattern in interface_patterns:
        if pattern in content:
            print(f"  ✓ 接口模式: {pattern}")
        else:
            print(f"  ✗ 缺少接口模式: {pattern}")
            return False
    
    return True

def main():
    """运行所有对齐性检查。"""
    print("验证 Qwen2MoE 与 PR #18343 的对齐性...\n")
    
    checks = [
        check_enable_eplb_parameter,
        check_config_acquisition,
        check_fusedmoe_parameters,
        check_parameter_propagation,
        check_moe_attributes,
        check_interface_implementation,
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        if check():
            passed += 1
        print()
    
    print(f"结果: {passed}/{total} 检查通过")
    
    if passed == total:
        print("🎉 所有检查通过！Qwen2MoE 与 PR #18343 完全对齐。")
        return 0
    else:
        print("❌ 部分检查失败。请检查实现。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
